; boot.asm - Enhanced Bootloader with <PERSON>rro<PERSON> Handling
[bits 16]
[org 0x7C00]

start:
    ; Initialize segments
    xor ax, ax
    mov ds, ax
    mov es, ax
    mov ss, ax
    mov sp, 0x7C00

    ; Display boot message
    mov si, boot_msg
    call print_str

    ; Enable A20 line
    call enable_a20
    
    ; Load kernel (128 sectors starting at LBA 2)
    mov si, loading_kernel_msg
    call print_str
    
    mov ax, 0x1000
    mov es, ax
    xor bx, bx
    mov ah, 0x42
    mov dl, 0x80
    lea si, [disk_pkt]
    int 0x13
    jc disk_error

    ; Load userland (1 sector at LBA 66)
    mov si, loading_userland_msg
    call print_str
    
    mov ax, 0x2000
    mov es, ax
    xor bx, bx
    lea si, [userland_pkt]
    mov ah, 0x42
    int 0x13
    jc userland_error

    ; TESTING: Skip userland and go directly to kernel
    mov si, launching_kernel_msg
    call print_str

    ; Debug: Check if kernel was loaded correctly
    mov ax, 0x1000
    mov es, ax
    mov al, [es:0x0000]     ; Read first byte of kernel
    add al, '0'             ; Convert to ASCII (assuming it's a small number)
    mov ah, 0x0E
    mov bh, 0
    int 0x10                ; Display first byte

    ; Add a newline
    mov al, 13
    mov ah, 0x0E
    int 0x10
    mov al, 10
    mov ah, 0x0E
    int 0x10

    jmp 0x1000:0x0000

; Safe userland call with error isolation
safe_userland_call:
    ; Save all registers
    pusha
    push ds
    push es
    
    ; Set up error handler
    push .userland_error_handler
    
    ; Try to call userland (correct entry point at offset 0x200)
    call 0x2000:0x0200
    
    ; If we get here, userland worked
    add sp, 2  ; Remove error handler
    jmp .userland_cleanup
    
.userland_error_handler:
    ; Userland failed - show message and continue
    mov si, userland_failed_msg
    call print_str
    
.userland_cleanup:
    ; Restore all registers
    pop es
    pop ds
    popa
    ret

; Enable A20 line
enable_a20:
    ; Method 1: Fast A20
    in al, 0x92
    test al, 2
    jnz .a20_done
    or al, 2
    and al, 0xFE
    out 0x92, al
    
.a20_done:
    ret

disk_error:
    mov si, disk_err_msg
    call print_str
    jmp hang

userland_error:
    mov si, userland_err_msg
    call print_str
    ; Continue without userland
    mov si, continuing_msg
    call print_str
    jmp 0x1000:0x0000

hang:
    cli
    hlt
    jmp hang

; Print string function
print_str:
    push ax
    push si
    
.print_loop:
    lodsb
    or al, al
    jz .done
    mov ah, 0x0E
    mov bh, 0
    int 0x10
    jmp .print_loop
    
.done:
    pop si
    pop ax
    ret

; Disk packet for kernel loading
disk_pkt:
    db 0x10, 0x00      ; Packet size, reserved
    dw 128             ; Sectors to read (kernel)
    dw 0x0000          ; Offset
    dw 0x1000          ; Segment
    dq 2               ; LBA start (kernel at sector 2)

; Disk packet for userland loading
userland_pkt:
    db 0x10, 0x00      ; Packet size, reserved
    dw 4               ; Sectors to read (userland)
    dw 0x0000          ; Offset
    dw 0x2000          ; Segment
    dq 66              ; LBA start (userland at sector 66)

; Boot messages
boot_msg            db "FRACTAL OS BOOTLOADER v2.0", 13, 10, 0
loading_kernel_msg  db "Loading kernel...", 13, 10, 0
loading_userland_msg db "Loading userland...", 13, 10, 0
launching_userland_msg db "Launching userland...", 13, 10, 0
launching_kernel_msg db "Launching kernel...", 13, 10, 0
disk_err_msg        db "DISK ERROR - Cannot load kernel", 13, 10, 0
userland_err_msg    db "USERLAND LOAD ERROR", 13, 10, 0
userland_failed_msg db "USERLAND FAILED - Continuing without UI", 13, 10, 0
continuing_msg      db "Continuing to kernel...", 13, 10, 0

; Boot sector signature
times 510-($-$$) db 0
dw 0xAA55